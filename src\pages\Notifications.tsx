
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useApp } from '@/context/AppContext';
import { <PERSON>, CheckCircle, <PERSON><PERSON><PERSON><PERSON>gle, Clock } from 'lucide-react';

const Notifications: React.FC = () => {
  const { notifications, markNotificationAsRead, stagiaires } = useApp();

  const handleMarkAsRead = (id: string) => {
    markNotificationAsRead(id);
  };

  const markAllAsRead = () => {
    notifications.forEach(notification => {
      if (!notification.isRead) {
        markNotificationAsRead(notification.id);
      }
    });
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-foreground">Notifications</h1>
        {unreadCount > 0 && (
          <Button onClick={markAllAsRead} variant="outline">
            Marquer tout comme lu ({unreadCount})
          </Button>
        )}
      </div>

      {notifications.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Aucune notification</h3>
            <p className="text-muted-foreground">
              Vous serez notifié ici lorsque des stagiaires auront des notes insuffisantes
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {notifications
            .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
            .map((notification) => {
              const stagiaire = stagiaires.find(s => s.id === notification.stagiaireId);
              
              return (
                <Card 
                  key={notification.id} 
                  className={`transition-all hover:shadow-md ${
                    notification.isRead ? 'opacity-75' : 'border-yellow-200 bg-yellow-50/50'
                  }`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-full ${
                          notification.type === 'warning' 
                            ? 'bg-yellow-100 text-yellow-600' 
                            : 'bg-red-100 text-red-600'
                        }`}>
                          <AlertTriangle className="h-4 w-4" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge 
                              variant={notification.type === 'warning' ? 'destructive' : 'default'}
                              className="text-xs"
                            >
                              {notification.type === 'warning' ? 'Attention' : 'Erreur'}
                            </Badge>
                            {!notification.isRead && (
                              <Badge variant="outline" className="text-xs">
                                Nouveau
                              </Badge>
                            )}
                          </div>
                          
                          <p className="text-sm font-medium text-foreground mb-1">
                            {notification.message}
                          </p>
                          
                          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>
                                {new Date(notification.date).toLocaleDateString('fr-FR', {
                                  day: '2-digit',
                                  month: '2-digit',
                                  year: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </span>
                            </div>
                            {stagiaire && (
                              <span>
                                Stagiaire: {stagiaire.prenom} {stagiaire.nom}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {notification.isRead ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleMarkAsRead(notification.id)}
                          >
                            Marquer comme lu
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
        </div>
      )}

      {/* Statistiques des notifications */}
      {notifications.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Statistiques des notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-foreground">{notifications.length}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{unreadCount}</div>
                <div className="text-sm text-muted-foreground">Non lues</div>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {notifications.filter(n => n.isRead).length}
                </div>
                <div className="text-sm text-muted-foreground">Lues</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Notifications;
