
export interface Stage {
  id: string;
  nom: string;
  description: string;
  dateDebut: string;
  dateFin: string;
  formateur: string;
}

export interface Stagiaire {
  id: string;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  stageId: string;
}

export interface Matiere {
  id: string;
  nom: string;
  coefficient: number;
}

export interface Note {
  id: string;
  stagiaireId: string;
  matiereId: string;
  note: number;
  coefficient: number;
  date: string;
}

export interface Notification {
  id: string;
  message: string;
  type: 'warning' | 'error' | 'info';
  stagiaireId: string;
  isRead: boolean;
  date: string;
}
