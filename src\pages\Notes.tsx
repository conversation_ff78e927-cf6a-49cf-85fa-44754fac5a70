
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useApp } from '@/context/AppContext';
import { Plus, FileSpreadsheet, User, BookOpen, Calendar } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const Notes: React.FC = () => {
  const { notes, addNote, stagiaires, matieres, calculateMoyenne } = useApp();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    stagiaireId: '',
    matiereId: '',
    note: '',
    date: new Date().toISOString().split('T')[0]
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const matiere = matieres.find(m => m.id === formData.matiereId);
    if (matiere) {
      addNote({
        ...formData,
        note: parseFloat(formData.note),
        coefficient: matiere.coefficient
      });
      setFormData({
        stagiaireId: '',
        matiereId: '',
        note: '',
        date: new Date().toISOString().split('T')[0]
      });
      setIsDialogOpen(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSelectChange = (field: string) => (value: string) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  const handleFileImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Simulation d'import Excel - dans un vrai projet, vous utiliseriez une bibliothèque comme xlsx
      console.log('Import du fichier:', file.name);
      // Ici vous pourriez parser le fichier Excel et ajouter les notes
      setIsImportDialogOpen(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-foreground">Gestion des notes</h1>
        <div className="flex space-x-2">
          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center space-x-2">
                <FileSpreadsheet className="h-4 w-4" />
                <span>Importer Excel</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Importer les notes depuis Excel</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Sélectionnez un fichier Excel contenant les notes des stagiaires.
                  Le fichier doit contenir les colonnes: Stagiaire, Matière, Note.
                </p>
                <Input
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleFileImport}
                />
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                    Annuler
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Nouvelle note</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Ajouter une nouvelle note</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="stagiaireId">Stagiaire</Label>
                  <Select value={formData.stagiaireId} onValueChange={handleSelectChange('stagiaireId')}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un stagiaire" />
                    </SelectTrigger>
                    <SelectContent>
                      {stagiaires.map((stagiaire) => (
                        <SelectItem key={stagiaire.id} value={stagiaire.id}>
                          {stagiaire.prenom} {stagiaire.nom}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="matiereId">Matière</Label>
                  <Select value={formData.matiereId} onValueChange={handleSelectChange('matiereId')}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner une matière" />
                    </SelectTrigger>
                    <SelectContent>
                      {matieres.map((matiere) => (
                        <SelectItem key={matiere.id} value={matiere.id}>
                          {matiere.nom} (coef. {matiere.coefficient})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="note">Note (sur 20)</Label>
                  <Input
                    id="note"
                    name="note"
                    type="number"
                    min="0"
                    max="20"
                    step="0.5"
                    value={formData.note}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="date">Date</Label>
                  <Input
                    id="date"
                    name="date"
                    type="date"
                    value={formData.date}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                
                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Annuler
                  </Button>
                  <Button type="submit" disabled={!formData.stagiaireId || !formData.matiereId}>
                    Ajouter
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Tableau des moyennes */}
      <Card>
        <CardHeader>
          <CardTitle>Moyennes des stagiaires</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Stagiaire</TableHead>
                <TableHead>Notes saisies</TableHead>
                <TableHead>Moyenne générale</TableHead>
                <TableHead>Statut</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {stagiaires.map((stagiaire) => {
                const stagiaireNotes = notes.filter(n => n.stagiaireId === stagiaire.id);
                const moyenne = calculateMoyenne(stagiaire.id);
                
                return (
                  <TableRow key={stagiaire.id}>
                    <TableCell className="font-medium">
                      {stagiaire.prenom} {stagiaire.nom}
                    </TableCell>
                    <TableCell>{stagiaireNotes.length}</TableCell>
                    <TableCell>
                      {moyenne > 0 ? `${moyenne.toFixed(2)}/20` : 'Aucune note'}
                    </TableCell>
                    <TableCell>
                      {moyenne === 0 ? (
                        <Badge variant="outline">Sans notes</Badge>
                      ) : moyenne >= 12 ? (
                        <Badge className="bg-green-100 text-green-800">Admis</Badge>
                      ) : (
                        <Badge className="bg-red-100 text-red-800">En difficulté</Badge>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Tableau des notes détaillées */}
      <Card>
        <CardHeader>
          <CardTitle>Toutes les notes</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Stagiaire</TableHead>
                <TableHead>Matière</TableHead>
                <TableHead>Note</TableHead>
                <TableHead>Coefficient</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {notes.map((note) => {
                const stagiaire = stagiaires.find(s => s.id === note.stagiaireId);
                const matiere = matieres.find(m => m.id === note.matiereId);
                
                return (
                  <TableRow key={note.id}>
                    <TableCell>
                      {stagiaire ? `${stagiaire.prenom} ${stagiaire.nom}` : 'Stagiaire non trouvé'}
                    </TableCell>
                    <TableCell>{matiere?.nom || 'Matière non trouvée'}</TableCell>
                    <TableCell>
                      <Badge className={note.note >= 12 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {note.note}/20
                      </Badge>
                    </TableCell>
                    <TableCell>{note.coefficient}</TableCell>
                    <TableCell>{new Date(note.date).toLocaleDateString('fr-FR')}</TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          
          {notes.length === 0 && (
            <div className="text-center py-8">
              <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Aucune note</h3>
              <p className="text-muted-foreground">
                Commencez par ajouter des notes pour vos stagiaires
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Notes;
