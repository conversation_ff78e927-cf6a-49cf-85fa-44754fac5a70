
import React, { createContext, useContext, ReactNode } from 'react';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import { Stage, Stagiaire, Note, Notification, Matiere } from '@/types';

interface AppContextType {
  stages: Stage[];
  stagiaires: Stagiaire[];
  notes: Note[];
  notifications: Notification[];
  matieres: <PERSON>ier<PERSON>[];
  addStage: (stage: Omit<Stage, 'id'>) => void;
  addStagiaire: (stagiaire: Omit<Stagiaire, 'id'>) => void;
  addNote: (note: Omit<Note, 'id'>) => void;
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  markNotificationAsRead: (id: string) => void;
  calculateMoyenne: (stagiaireId: string) => number;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

const defaultMatieres: Matiere[] = [
  { id: '1', nom: 'Mathématiques', coefficient: 3 },
  { id: '2', nom: 'Français', coefficient: 2 },
  { id: '3', nom: 'Informatique', coefficient: 4 },
  { id: '4', nom: 'Anglais', coefficient: 2 },
  { id: '5', nom: 'Gestion de projet', coefficient: 3 },
];

export function AppProvider({ children }: { children: ReactNode }) {
  const [stages, setStages] = useLocalStorage<Stage[]>('stages', []);
  const [stagiaires, setStagiaires] = useLocalStorage<Stagiaire[]>('stagiaires', []);
  const [notes, setNotes] = useLocalStorage<Note[]>('notes', []);
  const [notifications, setNotifications] = useLocalStorage<Notification[]>('notifications', []);
  const [matieres] = useLocalStorage<Matiere[]>('matieres', defaultMatieres);

  const addStage = (stage: Omit<Stage, 'id'>) => {
    const newStage: Stage = {
      ...stage,
      id: Date.now().toString(),
    };
    setStages([...stages, newStage]);
  };

  const addStagiaire = (stagiaire: Omit<Stagiaire, 'id'>) => {
    const newStagiaire: Stagiaire = {
      ...stagiaire,
      id: Date.now().toString(),
    };
    setStagiaires([...stagiaires, newStagiaire]);
  };

  const addNote = (note: Omit<Note, 'id'>) => {
    const newNote: Note = {
      ...note,
      id: Date.now().toString(),
    };
    setNotes([...notes, newNote]);

    // Vérifier si la note est inférieure à 12 et créer une notification
    if (note.note < 12) {
      const stagiaire = stagiaires.find(s => s.id === note.stagiaireId);
      const matiere = matieres.find(m => m.id === note.matiereId);
      
      if (stagiaire && matiere) {
        addNotification({
          message: `${stagiaire.prenom} ${stagiaire.nom} a obtenu ${note.note}/20 en ${matiere.nom} (coefficient ${note.coefficient})`,
          type: 'warning',
          stagiaireId: note.stagiaireId,
          isRead: false,
          date: new Date().toISOString(),
        });
      }
    }
  };

  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
    };
    setNotifications([...notifications, newNotification]);
  };

  const markNotificationAsRead = (id: string) => {
    setNotifications(notifications.map(n => 
      n.id === id ? { ...n, isRead: true } : n
    ));
  };

  const calculateMoyenne = (stagiaireId: string): number => {
    const stagiaireNotes = notes.filter(n => n.stagiaireId === stagiaireId);
    if (stagiaireNotes.length === 0) return 0;

    const totalPoints = stagiaireNotes.reduce((sum, note) => sum + (note.note * note.coefficient), 0);
    const totalCoefficients = stagiaireNotes.reduce((sum, note) => sum + note.coefficient, 0);
    
    return totalCoefficients > 0 ? totalPoints / totalCoefficients : 0;
  };

  return (
    <AppContext.Provider value={{
      stages,
      stagiaires,
      notes,
      notifications,
      matieres,
      addStage,
      addStagiaire,
      addNote,
      addNotification,
      markNotificationAsRead,
      calculateMoyenne,
    }}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
