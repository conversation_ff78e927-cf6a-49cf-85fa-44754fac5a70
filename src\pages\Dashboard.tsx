
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useApp } from '@/context/AppContext';
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { Users, GraduationCap, FileText, AlertTriangle } from 'lucide-react';

const Dashboard: React.FC = () => {
  const { stages, stagiaires, notes, notifications, calculateMoyenne } = useApp();
  
  const unreadNotifications = notifications.filter(n => !n.isRead).length;
  
  // Calcul des statistiques
  const moyennesData = stagiaires.map(stagiaire => ({
    nom: `${stagiaire.prenom} ${stagiaire.nom}`,
    moyenne: Math.round(calculateMoyenne(stagiaire.id) * 100) / 100
  }));

  const reussiteData = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON> (≥12)',
      value: stagiaires.filter(s => calculateMoyenne(s.id) >= 12).length,
      color: '#10b981'
    },
    {
      name: 'En difficulté (<12)',
      value: stagiaires.filter(s => calculateMoyenne(s.id) < 12 && calculateMoyenne(s.id) > 0).length,
      color: '#f59e0b'
    },
    {
      name: 'Sans notes',
      value: stagiaires.filter(s => calculateMoyenne(s.id) === 0).length,
      color: '#6b7280'
    }
  ];

  const stats = [
    {
      title: 'Stages actifs',
      value: stages.length,
      icon: GraduationCap,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Stagiaires',
      value: stagiaires.length,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Notes saisies',
      value: notes.length,
      icon: FileText,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Alertes',
      value: unreadNotifications,
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-foreground">Tableau de bord</h1>
        <Badge variant="outline" className="text-sm">
          {new Date().toLocaleDateString('fr-FR')}
        </Badge>
      </div>

      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                    <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Graphique des moyennes */}
        <Card>
          <CardHeader>
            <CardTitle>Moyennes des stagiaires</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={moyennesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="nom" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  interval={0}
                />
                <YAxis domain={[0, 20]} />
                <Tooltip />
                <Bar dataKey="moyenne" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Graphique de répartition */}
        <Card>
          <CardHeader>
            <CardTitle>Répartition des résultats</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={reussiteData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {reussiteData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Dernières notifications */}
      {notifications.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Dernières notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {notifications.slice(0, 5).map((notification) => (
                <div 
                  key={notification.id}
                  className={`p-3 rounded-lg border ${
                    notification.isRead ? 'bg-muted/50' : 'bg-yellow-50 border-yellow-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <p className="text-sm">{notification.message}</p>
                    <Badge variant={notification.type === 'warning' ? 'destructive' : 'default'}>
                      {notification.type}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {new Date(notification.date).toLocaleDateString('fr-FR')}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Dashboard;
